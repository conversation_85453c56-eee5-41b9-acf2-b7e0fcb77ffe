import { Body, Controller, Get, Param, Post, Req, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';
import { MobileService } from './mobile.service';
import { Request } from 'express';
import { CreateMobileComplaintDto } from './dto/create-mobile-complaint.dto';
import { CreateMobileFacilityIssueReportDto } from './dto/create-mobile-facility-report.dto';
import { CreateMobileReservationDto } from './dto/create-mobile-reservation.dto';
import { AuthGuard } from 'src/common/guards/auth/auth.guard';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtService } from '@nestjs/jwt';

@UseGuards(AuthGuard)
@Controller('mobile')
export class MobileController {
  constructor(
    private readonly mobileService: MobileService,
    private readonly jwtService: JwtService,
  ) {}

  @Get('me')
  async findOne(@Req() req: Request) {
    const user = (req as any).user;
    return this.mobileService.findOneForMobile(user.sub);
  }

  @Get('phone-directory')
  async findAllPhoneDirectory() {
    return this.mobileService.findAllPhoneDirectory();
  }

  @Get('facilities')
  async findAllFacilites() {
    return this.mobileService.findAllFacilities();
  }

  @Get('complaint-types')
  async findAllComplaintTypes() {
    return this.mobileService.findAllComplaintTypes();
  }

  //Reservations

  @Post('reservation')
  async createReservation(@Body() dto: CreateMobileReservationDto, @Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.createReservation(dto, payload.sub);
  }

  @Get('reservation')
  async findAllReservations(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllReservations(payload.sub);
  }

  //Complaints

  @Get('complaint')
  async findAllComplaints(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllComplaints(payload.sub);
  }

  @Post('complaint')
  @UseInterceptors(FilesInterceptor('files', 3))
  async createComplaint(
    @Body() dto: CreateMobileComplaintDto,
    @Req() req: Request,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.createComplaint(dto, payload.sub, files);
  }

  //Maintenance Issue Reports
  @Get('maintenance-issue-report')
  async getAllMaintenanceIssueReports(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllMaintenanceIssueReports(payload.sub);
  }

  @Post('maintenance-issue-report')
  @UseInterceptors(FilesInterceptor('files', 3))
  async createMaintenanceIssueReport(
    @Body() dto: CreateMobileFacilityIssueReportDto,
    @Req() req: Request,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.createMaintenanceIssueReport(dto, payload.sub, files);
  }

  //Properties
  @Get('property')
  async getAllProperties(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllProperties(payload.sub);
  }

  //get residents by propertyid
  @Get('property/:propertyId/residents')
  async getResidentsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getResidentsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/vehicles')
  async getVehiclesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getVehiclesByPropertyId(propertyId);
  }

  @Get('property/:propertyId/pets')
  async getPetsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getPetsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/tags')
  async getTagsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getTagsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/parking-spots')
  async getParkingSpotsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getParkingSpotsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/reservations')
  async getReservationsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getReservationsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/maintenance-issue-reports')
  async getMaintenanceIssueReportsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getMaintenanceIssueReportsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/infractions')
  async getInfractionsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getInfractionsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/complaints')
  async getComplaintsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getComplaintsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/fines')
  async getFinesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getFinesByPropertyId(propertyId);
  }

  @Get('property/:propertyId/monthly-maintenance-charges')
  async getMonthlyMaintenanceChargesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getMonthlyMaintenanceChargesByPropertyId(propertyId);
  }
}
