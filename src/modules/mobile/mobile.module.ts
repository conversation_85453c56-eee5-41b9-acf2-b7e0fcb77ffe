import { Modu<PERSON> } from '@nestjs/common';
import { MobileService } from './mobile.service';
import { MobileController } from './mobile.controller';
import { ReservationService } from '../business-logic/reservation/reservation.service';
import { ComplaintService } from '../business-logic/complaint/complaint.service';
import { UserService } from '../business-logic/user/user.service';
import { PersistenceModule } from '../persistence/persistence.module';
import { MailService } from '../mail/mail.service';
import { FacilityService } from '../business-logic/facility/facility.service';
import { SupabaseService } from '../storage/supabase.service';
import { MaintenanceIssueReportService } from '../business-logic/maintenance-issue-report/maintenance-issue-report.service';
import { PhoneDirectoryService } from '../business-logic/phone-directory/phone-directory.service';
import { StorageModule } from '../storage/storage.module';
import { PropertyService } from '../business-logic/property/property.service';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [MobileController],
  providers: [
    MailService,
    UserService,
    MobileService,
    ComplaintService,
    ReservationService,
    MaintenanceIssueReportService,
    FacilityService,
    SupabaseService,
    PhoneDirectoryService,
    ComplaintService,
    PropertyService,
  ],
})
export class MobileModule {}
