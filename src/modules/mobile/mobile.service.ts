import { Injectable } from '@nestjs/common';
import { CreateMobileComplaintDto } from './dto/create-mobile-complaint.dto';
import { Status, User } from '@prisma/client';
import { CreateMobileFacilityIssueReportDto } from './dto/create-mobile-facility-report.dto';
import { CreateMobileReservationDto } from './dto/create-mobile-reservation.dto';
import { UserService } from '../business-logic/user/user.service';
import { FacilityService } from '../business-logic/facility/facility.service';
import { MaintenanceIssueReportService } from '../business-logic/maintenance-issue-report/maintenance-issue-report.service';
import { PhoneDirectoryService } from '../business-logic/phone-directory/phone-directory.service';
import { SupabaseService } from '../storage/supabase.service';
import { ComplaintService } from '../business-logic/complaint/complaint.service';
import { ReservationService } from '../business-logic/reservation/reservation.service';
import { PropertyService } from '../business-logic/property/property.service';

@Injectable()
export class MobileService {
  constructor(
    private readonly maintenanceIssueReportService: MaintenanceIssueReportService,
    private readonly userService: UserService,
    private readonly facilityService: FacilityService,
    private readonly phoneDirectoryService: PhoneDirectoryService,
    private readonly supabaseService: SupabaseService,
    private readonly complaintService: ComplaintService,
    private readonly reservationService: ReservationService,
    private readonly propertyService: PropertyService,
  ) {}

  async findOneForMobile(id: User['id']) {
    const user = await this.userService.findOneForMobile(id);
    if (user.announcements.length > 0) {
      for (const announcement of user.announcements) {
        if (announcement.images.length > 0) {
          announcement.images = await Promise.all(
            announcement.images.map(async (image) => {
              const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
              return {
                ...image,
                path: signedUrl,
              };
            }),
          );
        }
      }
    }
    return user;
  }

  async createComplaint(dto: CreateMobileComplaintDto, userId: string, files: Express.Multer.File[]) {
    return this.complaintService.createComplaint({ ...dto, userId, status: Status.OPEN }, files);
  }

  async createReservation(dto: CreateMobileReservationDto, userId: string) {
    return this.reservationService.create({
      ...dto,
      requestedBy: userId,
    });
  }

  async createMaintenanceIssueReport(
    dto: CreateMobileFacilityIssueReportDto,
    userId: string,
    files: Express.Multer.File[],
  ) {
    return this.maintenanceIssueReportService.createMaintenanceIssueReport(
      {
        ...dto,
        reportedBy: userId,
      },
      files,
    );
  }

  async findAllFacilities() {
    return this.facilityService.findAllForMobile();
  }

  async findAllPhoneDirectory() {
    return this.phoneDirectoryService.findAll();
  }

  async findAllComplaintTypes() {
    return this.complaintService.findAllComplaintTypes();
  }

  async getAllComplaints(userId: string) {
    return this.complaintService.getComplaintsByUserId(userId);
  }

  async getAllMaintenanceIssueReports(userId: string) {
    return this.maintenanceIssueReportService.getMaintenanceIssueReportsByUserId(userId);
  }

  async getAllReservations(userId: string) {
    return this.reservationService.getReservationsByUserId(userId);
  }

  async getAllProperties(userId: string) {
    return this.propertyService.findForMobileByUserId(userId);
  }

  async getResidentsByPropertyId(propertyId: string) {
    return this.propertyService.getResidentsByPropertyId(propertyId);
  }

  async getVehiclesByPropertyId(propertyId: string) {
    return this.propertyService.getVehiclesByPropertyId(propertyId);
  }

  async getPetsByPropertyId(propertyId: string) {
    return this.propertyService.getPetsByPropertyId(propertyId);
  }

  async getTagsByPropertyId(propertyId: string) {
    return this.propertyService.getTagsByPropertyId(propertyId);
  }

  async getParkingSpotsByPropertyId(propertyId: string) {
    return this.propertyService.getParkingSpotsByPropertyId(propertyId);
  }

  async getReservationsByPropertyId(propertyId: string) {
    return this.propertyService.getReservationsByPropertyId(propertyId);
  }

  async getMaintenanceIssueReportsByPropertyId(propertyId: string) {
    return this.propertyService.getMaintenanceIssueReportsByPropertyId(propertyId);
  }

  async getInfractionsByPropertyId(propertyId: string) {
    return this.propertyService.getInfractionsByPropertyId(propertyId);
  }

  async getComplaintsByPropertyId(propertyId: string) {
    return this.propertyService.getComplaintsByPropertyId(propertyId);
  }

  async getFinesByPropertyId(propertyId: string) {
    return this.propertyService.getFinesByPropertyId(propertyId);
  }

  async getMonthlyMaintenanceChargesByPropertyId(propertyId: string) {
    return this.propertyService.getMonthlyMaintenanceChargesByPropertyId(propertyId);
  }
}
